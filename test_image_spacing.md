# 测试图片间距控制

这个文档演示如何通过在图片文本中间添加空格来控制图片间距。

## 默认间距（无空格）
![图片1](test_images/image1.jpg)![图片2](test_images/image2.jpg)

## 两个空格间距
![图片1](test_images/image1.jpg)  ![图片2](test_images/image2.jpg)

## 四个空格间距
![图片1](test_images/image1.jpg)    ![图片2](test_images/image2.jpg)

## 六个空格间距
![图片1](test_images/image1.jpg)      ![图片2](test_images/image2.jpg)

## 八个空格间距
![图片1](test_images/image1.jpg)        ![图片2](test_images/image2.jpg)

## 十个空格间距
![图片1](test_images/image1.jpg)          ![图片2](test_images/image2.jpg)

## 三张图片的间距控制
![图片1](test_images/image1.jpg)  ![图片2](test_images/image2.jpg)    ![图片3](test_images/image3.jpg)

## 使用参数控制间距（备选方法）
![图片1](test_images/image1.jpg?spacing=30) ![图片2](test_images/image2.jpg)

## 说明
- 每个空格对应5像素的间距
- 最小间距为10像素，最大间距为100像素
- 空格数量 × 5 = 间距像素数
- 例如：4个空格 = 20像素间距
