# 测试图片间距控制

这个文档演示如何通过在图片文本中间添加空格来控制图片间距。

## 默认间距（紧贴）
![图片1](https://via.placeholder.com/150x100/ff0000/ffffff?text=Image1)![图片2](https://via.placeholder.com/150x100/00ff00/ffffff?text=Image2)

## 两个空格间距（10像素）
![图片1](https://via.placeholder.com/150x100/ff0000/ffffff?text=Image1)  ![图片2](https://via.placeholder.com/150x100/00ff00/ffffff?text=Image2)

## 四个空格间距（20像素）
![图片1](https://via.placeholder.com/150x100/ff0000/ffffff?text=Image1)    ![图片2](https://via.placeholder.com/150x100/00ff00/ffffff?text=Image2)

## 六个空格间距（30像素）
![图片1](https://via.placeholder.com/150x100/ff0000/ffffff?text=Image1)      ![图片2](https://via.placeholder.com/150x100/00ff00/ffffff?text=Image2)

## 八个空格间距（40像素）
![图片1](https://via.placeholder.com/150x100/ff0000/ffffff?text=Image1)        ![图片2](https://via.placeholder.com/150x100/00ff00/ffffff?text=Image2)

## 十个空格间距（50像素）
![图片1](https://via.placeholder.com/150x100/ff0000/ffffff?text=Image1)          ![图片2](https://via.placeholder.com/150x100/00ff00/ffffff?text=Image2)

## 三张图片的间距控制
![图片1](https://via.placeholder.com/100x80/ff0000/ffffff?text=1)  ![图片2](https://via.placeholder.com/100x80/00ff00/ffffff?text=2)    ![图片3](https://via.placeholder.com/100x80/0000ff/ffffff?text=3)

## 使用参数控制间距（备选方法）
![图片1](https://via.placeholder.com/150x100/ff0000/ffffff?text=Image1?spacing=30) ![图片2](https://via.placeholder.com/150x100/00ff00/ffffff?text=Image2)

## 说明
- 每个空格对应5像素的间距
- 最小间距为10像素，最大间距为100像素
- 空格数量 × 5 = 间距像素数
- 例如：4个空格 = 20像素间距
- 使用在线占位图片进行测试
